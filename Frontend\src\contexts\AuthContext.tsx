'use client';

import React, { createContext, useContext, useState, ReactNode } from 'react';

// 用户类型
export interface User {
  id: number;
  username: string;
  email: string;
  role: string;
  avatar?: string;
  name?: string;
}

// 认证状态
interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
}

// 认证操作
interface AuthActions {
  login: (email: string, password: string) => Promise<boolean>;
  register: (username: string, email: string, password: string) => Promise<boolean>;
  logout: () => void;
  setError: (error: string) => void;
  clearError: () => void;
  updateUser: (userData: Partial<User>) => void;
  resetPassword: (email: string) => Promise<boolean>;
  isLoading: boolean;
  error: string;
}

// Context类型
type AuthContextType = AuthState & AuthActions;

// 创建Context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// API URL
const API_URL = 'http://localhost:5000/api';

// AuthProvider组件
export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [error, setError] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(true); // 初始化时设为 true

  // 计算认证状态
  const isAuthenticated = !!(user && token);

  // 登录函数
  const login = async (email: string, password: string): Promise<boolean> => {
    try {
      setIsLoading(true);
      setError('');
      console.log('🔐 开始登录:', email);

      const response = await fetch(`${API_URL}/users/login`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ identifier: email, password }),
      });

      const data = await response.json();
      console.log('📡 登录响应:', data);

      if (data.success && data.user && data.token) {
        // 设置状态
        setUser(data.user);
        setToken(data.token);

        // 保存到localStorage
        localStorage.setItem('auth_token', data.token);
        localStorage.setItem('auth_user', JSON.stringify(data.user));

        console.log('✅ 登录成功，用户:', data.user.username);
        console.log('🔑 Token已保存:', data.token.substring(0, 20) + '...');
        return true;
      } else {
        console.log('❌ 登录失败:', data.message);
        setError(data.message || '登录失败');
        return false;
      }
    } catch (error) {
      console.error('❌ Login error:', error);
      setError('Network error, please try again');
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Register function
  const register = async (username: string, email: string, password: string): Promise<boolean> => {
    try {
      setError('');
      console.log('📝 Starting registration:', email);

      const response = await fetch(`${API_URL}/users/register`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ username, email, password }),
      });

      const data = await response.json();
      console.log('📡 Registration response:', data);

      if (data.success) {
        console.log('✅ Registration successful');
        return true;
      } else {
        setError(data.message || 'Registration failed');
        return false;
      }
    } catch (error) {
      console.error('❌ Registration error:', error);
      setError('Network error, please try again');
      return false;
    }
  };

  // Helper function to clear cookie
  const clearCookie = (name: string) => {
    if (typeof document !== 'undefined') {
      document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
    }
  };

  // 登出函数
  const logout = () => {
    console.log('🚪 用户登出');
    setUser(null);
    setToken(null);
    setError('');

    // 清除localStorage
    localStorage.removeItem('auth_token');
    localStorage.removeItem('auth_user');

    // 清除cookies
    clearCookie('auth_token');
    clearCookie('user_role');

    console.log('🗑️ 已清除所有认证信息');
  };

  // 清除错误
  const clearError = () => {
    setError('');
  };

  // 更新用户信息
  const updateUser = (userData: Partial<User>) => {
    if (user) {
      const updatedUser = { ...user, ...userData };
      setUser(updatedUser);
      localStorage.setItem('auth_user', JSON.stringify(updatedUser));
    }
  };

  // 重置密码
  const resetPassword = async (email: string): Promise<boolean> => {
    try {
      setIsLoading(true);
      setError('');

      const response = await fetch(`${API_URL}/auth/forgot-password`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      const data = await response.json();

      if (response.ok && data.success) {
        return true;
      } else {
        setError(data.message || 'Password reset failed');
        return false;
      }
    } catch (error) {
      setError('Network error occurred');
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // 初始化认证状态（从localStorage恢复）
  React.useEffect(() => {
    const initAuth = () => {
      try {
        const savedToken = localStorage.getItem('auth_token');
        const savedUser = localStorage.getItem('auth_user');

        console.log('🔍 AuthContext初始化 - 检查存储:', {
          hasToken: !!savedToken,
          hasUser: !!savedUser
        });

        if (savedToken && savedUser) {
          try {
            const userData = JSON.parse(savedUser);
            console.log('🔄 恢复用户状态:', userData.username);

            // 立即设置状态
            setUser(userData);
            setToken(savedToken);

            console.log('✅ 认证状态已恢复');
          } catch (parseError) {
            console.error('❌ 解析用户数据失败:', parseError);
            localStorage.removeItem('auth_token');
            localStorage.removeItem('auth_user');
          }
        } else {
          console.log('📭 没有找到保存的认证信息');
        }
      } catch (error) {
        console.error('❌ 初始化认证状态失败:', error);
        localStorage.removeItem('auth_token');
        localStorage.removeItem('auth_user');
      }

      // 使用setTimeout确保状态更新完成后再设置loading为false
      setTimeout(() => {
        setIsLoading(false);
        console.log('✅ AuthContext初始化完成，isLoading设为false');
      }, 0);
    };

    // 立即执行初始化
    initAuth();
  }, []);

  const value: AuthContextType = {
    user,
    token,
    isAuthenticated,
    login,
    register,
    logout,
    error,
    setError,
    clearError,
    updateUser,
    resetPassword,
    isLoading,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

// useAuth hook
export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
'use client';

import { useState, useEffect } from 'react';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/components/Toast';
import FollowButton from '@/components/social/FollowButton';

interface UserProfile {
  id: number;
  username: string;
  name: string;
  avatar: string;
  bio?: string;
  followers: number;
  following: number;
  works: number;
  joinedAt: string;
  isFollowing: boolean;
  isVerified?: boolean;
}

interface UserProfileModalProps {
  isOpen: boolean;
  onClose: () => void;
  userId: number;
  username?: string;
}

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';

export default function UserProfileModal({ isOpen, onClose, userId, username }: UserProfileModalProps) {
  const { isAuthenticated, user, token } = useAuth();
  const toast = useToast();
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (isOpen && userId) {
      fetchUserProfile();
    }
  }, [isOpen, userId]);

  const fetchUserProfile = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const endpoint = username 
        ? `${API_BASE_URL}/users/profile/${username}`
        : `${API_BASE_URL}/users/${userId}`;
      
      const headers: HeadersInit = {
        'Content-Type': 'application/json',
      };
      
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      const response = await fetch(endpoint, { headers });
      
      if (response.ok) {
        const data = await response.json();
        setProfile(data.success ? data.data : data);
      } else {
        throw new Error('Failed to fetch user profile');
      }
    } catch (error) {
      console.error('Error fetching user profile:', error);
      setError('Failed to load user profile');
      toast.error('Error', 'Failed to load user profile');
    } finally {
      setLoading(false);
    }
  };

  const handleFollowChange = (isFollowing: boolean) => {
    if (profile) {
      setProfile(prev => prev ? {
        ...prev,
        isFollowing,
        followers: isFollowing ? prev.followers + 1 : prev.followers - 1
      } : null);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long'
    });
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-md w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b">
          <h2 className="text-lg font-semibold text-gray-900">User Profile</h2>
          <button
            onClick={onClose}
            className="p-1 hover:bg-gray-100 rounded-full transition-colors"
          >
            <XMarkIcon className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : error ? (
            <div className="text-center py-8">
              <p className="text-red-500">{error}</p>
              <button
                onClick={fetchUserProfile}
                className="mt-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                Retry
              </button>
            </div>
          ) : profile ? (
            <div className="space-y-4">
              {/* Avatar and Basic Info */}
              <div className="flex items-center space-x-4">
                <div className="w-16 h-16 rounded-full overflow-hidden bg-gradient-to-br from-blue-400 to-purple-500 flex-shrink-0">
                  {profile.avatar ? (
                    <img
                      src={profile.avatar}
                      alt={profile.name}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(profile.name)}&background=6366f1&color=fff&size=64`;
                      }}
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center">
                      <span className="text-white font-medium text-xl">
                        {profile.name.charAt(0).toUpperCase()}
                      </span>
                    </div>
                  )}
                </div>
                
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <h3 className="text-xl font-bold text-gray-900">{profile.name}</h3>
                    {profile.isVerified && (
                      <span className="text-blue-500 text-xs font-medium">✓ Verified</span>
                    )}
                  </div>
                  <p className="text-gray-600">@{profile.username}</p>
                </div>
              </div>

              {/* Bio */}
              {profile.bio && (
                <p className="text-gray-700 leading-relaxed">{profile.bio}</p>
              )}

              {/* Stats */}
              <div className="flex items-center gap-6 text-sm text-gray-600">
                <span><strong className="text-gray-900">{profile.followers}</strong> Followers</span>
                <span><strong className="text-gray-900">{profile.following}</strong> Following</span>
                <span><strong className="text-gray-900">{profile.works}</strong> Works</span>
              </div>

              {/* Join Date */}
              <div className="text-sm text-gray-600">
                📅 Joined {formatDate(profile.joinedAt)}
              </div>

              {/* Follow Button */}
              {isAuthenticated && user && user.id !== profile.id && (
                <div className="pt-2">
                  <FollowButton
                    userId={profile.id}
                    onFollowChange={handleFollowChange}
                  />
                </div>
              )}

              {/* Login Prompt for Anonymous Users */}
              {!isAuthenticated && (
                <div className="pt-2">
                  <button
                    onClick={() => {
                      toast.info('Please login to follow users');
                      onClose();
                    }}
                    className="w-full px-4 py-2 bg-gray-100 text-gray-500 rounded-lg cursor-not-allowed"
                  >
                    Login to Follow
                  </button>
                </div>
              )}

              {/* View Full Profile Button */}
              <div className="pt-2">
                <button
                  onClick={() => {
                    window.open(`/profile/${profile.username}`, '_blank');
                    onClose();
                  }}
                  className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  View Full Profile
                </button>
              </div>
            </div>
          ) : null}
        </div>
      </div>
    </div>
  );
}
